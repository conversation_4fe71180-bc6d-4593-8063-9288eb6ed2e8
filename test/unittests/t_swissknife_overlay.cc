/**
 * This file is part of the CernVM File System.
 */

#include <gtest/gtest.h>

#include <string>
#include <vector>

#include "swissknife_overlay.h"
#include "catalog.h"
#include "catalog_rw.h"
#include "catalog_sql.h"
#include "directory_entry.h"
#include "util/posix.h"
#include "util/string.h"
#include "../common/testutil.h"

using namespace std;  // NOLINT

class T_SwissknifeOverlay : public ::testing::Test {
 protected:
  virtual void SetUp() {
    sandbox_ = CreateTempDir("/tmp/cvmfs_unittest");
    ASSERT_FALSE(sandbox_.empty());
    
    command_overlay_ = new swissknife::CommandOverlay();
  }

  virtual void TearDown() {
    delete command_overlay_;
    if (!sandbox_.empty()) {
      RemoveTree(sandbox_);
    }
  }

  string CreateTestCatalog(const string &root_path, 
                          const vector<pair<string, bool> > &entries) {
    string catalog_path = CreateTempPath(sandbox_ + "/catalog", 0666);
    EXPECT_FALSE(catalog_path.empty());

    // Create catalog database
    catalog::CatalogDatabase *catalog_db = 
        catalog::CatalogDatabase::Create(catalog_path);
    EXPECT_NE(static_cast<catalog::CatalogDatabase *>(NULL), catalog_db);
    
    // Initialize with root entry
    const bool volatile_content = false;
    const string voms_authz = "";
    catalog::DirectoryEntry root_entry;
    bool retval = catalog_db->InsertInitialValues(root_path, volatile_content, 
                                                  voms_authz, root_entry);
    EXPECT_TRUE(retval);
    delete catalog_db;

    // Add entries to catalog
    catalog::WritableCatalog *catalog = 
        catalog::WritableCatalog::AttachFreely(root_path, catalog_path, 
                                               shash::Any());
    EXPECT_NE(static_cast<catalog::WritableCatalog *>(NULL), catalog);

    for (size_t i = 0; i < entries.size(); ++i) {
      const string &entry_name = entries[i].first;
      const bool is_directory = entries[i].second;

      catalog::DirectoryEntryTestFactory::Metadata metadata;
      metadata.name = entry_name;
      metadata.uid = 1000;
      metadata.gid = 1000;
      metadata.size = 1024;
      metadata.mtime = time(NULL);
      metadata.linkcount = 1;
      metadata.has_xattrs = false;
      metadata.is_hidden = false;
      metadata.checksum = shash::Any();

      if (is_directory) {
        metadata.mode = S_IFDIR | 0755;
      } else {
        metadata.mode = S_IFREG | 0644;
      }

      catalog::DirectoryEntry entry = catalog::DirectoryEntryTestFactory::Make(metadata);
      XattrList empty_xattrs;
      string entry_path = root_path.empty() ? entry_name : root_path + "/" + entry_name;
      string parent_path = root_path;

      catalog->AddEntry(entry, empty_xattrs, entry_path, parent_path);
    }

    catalog->Commit();
    delete catalog;
    
    return catalog_path;
  }

  string sandbox_;
  swissknife::CommandOverlay *command_overlay_;
};

TEST_F(T_SwissknifeOverlay, IsWhiteoutFile) {
  string target;
  
  // Test regular whiteout file
  EXPECT_TRUE(command_overlay_->IsWhiteoutFile(".wh.test.txt", &target));
  EXPECT_EQ("test.txt", target);
  
  // Test whiteout file without target parameter
  EXPECT_TRUE(command_overlay_->IsWhiteoutFile(".wh.another_file"));
  
  // Test non-whiteout file
  EXPECT_FALSE(command_overlay_->IsWhiteoutFile("normal_file.txt", &target));
  
  // Test file that starts with .wh but isn't a whiteout
  EXPECT_FALSE(command_overlay_->IsWhiteoutFile(".wh", &target));
  
  // Test empty filename
  EXPECT_FALSE(command_overlay_->IsWhiteoutFile("", &target));
}

TEST_F(T_SwissknifeOverlay, IsOpaqueDirectoryMarker) {
  // Test opaque directory marker
  EXPECT_TRUE(command_overlay_->IsOpaqueDirectoryMarker(".wh..wh..opq"));
  
  // Test non-opaque marker
  EXPECT_FALSE(command_overlay_->IsOpaqueDirectoryMarker(".wh.test.txt"));
  EXPECT_FALSE(command_overlay_->IsOpaqueDirectoryMarker("normal_file.txt"));
  EXPECT_FALSE(command_overlay_->IsOpaqueDirectoryMarker(""));
}

TEST_F(T_SwissknifeOverlay, BasicOverlay) {
  // Create lower catalog with some files
  vector<pair<string, bool> > lower_entries;
  lower_entries.push_back(make_pair("file1.txt", false));
  lower_entries.push_back(make_pair("file2.txt", false));
  lower_entries.push_back(make_pair("dir1", true));
  
  string lower_catalog = CreateTestCatalog("", lower_entries);
  
  // Create upper catalog with additional files and whiteouts
  vector<pair<string, bool> > upper_entries;
  upper_entries.push_back(make_pair("file3.txt", false));
  upper_entries.push_back(make_pair(".wh.file1.txt", false));  // Remove file1.txt
  
  string upper_catalog = CreateTestCatalog("", upper_entries);
  
  // Create output catalog path
  string output_catalog = CreateTempPath(sandbox_ + "/output_catalog", 0666);
  
  // Test the overlay operation
  LocalObjectFetcher<> fetcher(sandbox_, sandbox_);
  catalog::WritableCatalog *result = command_overlay_->OverlayCatalogPaths(
      lower_catalog, upper_catalog, output_catalog, &fetcher);

  EXPECT_NE(static_cast<catalog::WritableCatalog *>(NULL), result);
  
  if (result) {
    // Verify the result contains expected entries
    catalog::DirectoryEntryList entries;
    PathString root_path("");
    EXPECT_TRUE(result->ListingPath(root_path, &entries));
    
    // Should contain file2.txt, file3.txt, dir1, but NOT file1.txt (whited out)
    bool found_file2 = false, found_file3 = false, found_dir1 = false, found_file1 = false;
    
    for (size_t i = 0; i < entries.size(); ++i) {
      string name = entries[i].name().ToString();
      if (name == "file1.txt") found_file1 = true;
      if (name == "file2.txt") found_file2 = true;
      if (name == "file3.txt") found_file3 = true;
      if (name == "dir1") found_dir1 = true;
    }
    
    EXPECT_FALSE(found_file1);  // Should be whited out
    EXPECT_TRUE(found_file2);   // From lower layer
    EXPECT_TRUE(found_file3);   // From upper layer
    EXPECT_TRUE(found_dir1);    // From lower layer
    
    delete result;
  }
}

TEST_F(T_SwissknifeOverlay, GetParams) {
  swissknife::ParameterList params = command_overlay_->GetParams();

  // Should have at least lower, upper, and output parameters
  EXPECT_GE(params.size(), 3u);

  bool has_lower = false, has_upper = false, has_output = false;
  for (size_t i = 0; i < params.size(); ++i) {
    if (params[i].key() == 'l') has_lower = true;
    if (params[i].key() == 'u') has_upper = true;
    if (params[i].key() == 'o') has_output = true;
  }

  EXPECT_TRUE(has_lower);
  EXPECT_TRUE(has_upper);
  EXPECT_TRUE(has_output);
}

TEST_F(T_SwissknifeOverlay, GetName) {
  EXPECT_EQ("overlay", command_overlay_->GetName());
}

TEST_F(T_SwissknifeOverlay, GetDescription) {
  string description = command_overlay_->GetDescription();
  EXPECT_FALSE(description.empty());
  EXPECT_NE(string::npos, description.find("overlay"));
}
