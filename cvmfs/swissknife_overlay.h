/**
 * This file is part of the CernVM File System.
 */

#ifndef CVMFS_SWISSKNIFE_OVERLAY_H_
#define CVMFS_SWISSKNIFE_OVERLAY_H_

#include <string>
#include <vector>
#include <map>

#include "catalog_traversal.h"
#include "crypto/hash.h"
#include "directory_entry.h"
#include "object_fetcher.h"
#include "swissknife.h"
#include "util/string.h"

namespace catalog {
class Catalog;
class WritableCatalog;
}

namespace swissknife {

/**
 * This command overlays two CVMFS catalogs to create a new catalog.
 * It takes two catalog paths and creates a new catalog that is the overlay
 * of the two catalogs. The upper catalog is overlayed on top of the lower
 * catalog, with conflicting entries from the upper catalog taking preference.
 *
 * The tool respects Docker whiteout markers:
 * - .wh.<filename> - indicates that <filename> should be deleted/hidden
 * - .wh..wh..opq - indicates that the directory should be opaque (all previous
 *   content hidden)
 */
class CommandOverlay : public Command {
 public:
  CommandOverlay();
  ~CommandOverlay() { }

  virtual std::string GetName() const { return "overlay"; }
  virtual std::string GetDescription() const {
    return "Overlay two CVMFS catalogs to create a new catalog\n"
           "This command takes two catalog paths (lower and upper) and creates "
           "a new catalog at the specified output location that is the overlay "
           "of the two catalogs. The upper catalog is overlayed on top of the "
           "lower catalog. Docker whiteout files (.wh. prefix) are respected "
           "and cause entries to be hidden/deleted.";
  }

  virtual ParameterList GetParams() const;
  virtual int Main(const ArgumentList &args);

 protected:
  /**
   * Overlay two catalogs, creating a new catalog that combines them.
   * The upper_catalog is overlayed on top of the lower_catalog.
   * Returns a new WritableCatalog that the caller owns.
   */
  template<class ObjectFetcherT>
  catalog::WritableCatalog* OverlayCatalogs(
      const std::string &lower_catalog_path,
      const std::string &upper_catalog_path,
      const std::string &output_path,
      ObjectFetcherT *object_fetcher);

  /**
   * Recursively copy entries from source catalog to destination catalog,
   * applying whiteout rules and overlay logic.
   */
  bool CopyEntriesRecursively(
      const catalog::Catalog *source_catalog,
      catalog::WritableCatalog *dest_catalog,
      const std::string &current_path,
      bool is_upper_layer);

  /**
   * Check if a filename is a Docker whiteout marker
   */
  bool IsWhiteoutFile(const std::string &filename, std::string *target = nullptr);

  /**
   * Check if a filename is an opaque directory marker
   */
  bool IsOpaqueDirectoryMarker(const std::string &filename);

  /**
   * Get the parent directory path from a full path
   */
  std::string GetParentPath(const std::string &path);

 private:
  static const std::string kWhiteoutPrefix;
  static const std::string kOpaqueDirectoryMarker;
};

}  // namespace swissknife

#endif  // CVMFS_SWISSKNIFE_OVERLAY_H_
