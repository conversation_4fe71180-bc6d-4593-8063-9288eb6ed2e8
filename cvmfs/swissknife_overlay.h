/**
 * This file is part of the CernVM File System.
 */

#ifndef CVMFS_SWISSKNIFE_OVERLAY_H_
#define CVMFS_SWISSKNIFE_OVERLAY_H_

#include <string>
#include <vector>
#include <map>

#include "catalog_rw.h"
#include "catalog_traversal.h"
#include "crypto/hash.h"
#include "directory_entry.h"
#include "object_fetcher.h"
#include "swissknife.h"
#include "util/posix.h"
#include "util/string.h"

namespace catalog {
class Catalog;
class WritableCatalog;
}

namespace swissknife {

/**
 * This command overlays two CVMFS catalogs to create a new catalog.
 * It takes two catalog paths and creates a new catalog that is the overlay
 * of the two catalogs. The upper catalog is overlayed on top of the lower
 * catalog, with conflicting entries from the upper catalog taking preference.
 *
 * The tool respects Docker whiteout markers:
 * - .wh.<filename> - indicates that <filename> should be deleted/hidden
 * - .wh..wh..opq - indicates that the directory should be opaque (all previous
 *   content hidden)
 */
class CommandOverlay : public Command {
 public:
  CommandOverlay();
  ~CommandOverlay() { }

  virtual std::string GetName() const { return "overlay"; }
  virtual std::string GetDescription() const {
    return "Overlay two CVMFS catalogs to create a new catalog\n"
           "This command takes two catalog paths (lower and upper) and creates "
           "a new catalog at the specified output location that is the overlay "
           "of the two catalogs. The upper catalog is overlayed on top of the "
           "lower catalog. Docker whiteout files (.wh. prefix) are respected "
           "and cause entries to be hidden/deleted.";
  }

  virtual ParameterList GetParams() const;
  virtual int Main(const ArgumentList &args);

  /**
   * Check if a filename is a Docker whiteout marker
   */
  bool IsWhiteoutFile(const std::string &filename, std::string *target = nullptr);

  /**
   * Check if a filename is an opaque directory marker
   */
  bool IsOpaqueDirectoryMarker(const std::string &filename);

  /**
   * Overlay two catalogs, creating a new catalog that combines them.
   * The upper_catalog is overlayed on top of the lower_catalog.
   * Returns a new WritableCatalog that the caller owns.
   */
  template<class ObjectFetcherT>
  catalog::WritableCatalog* OverlayCatalogs(
      const std::string &lower_catalog_path,
      const std::string &upper_catalog_path,
      const std::string &output_path,
      ObjectFetcherT *object_fetcher);

 protected:

  /**
   * Recursively copy entries from source catalog to destination catalog,
   * applying whiteout rules and overlay logic.
   */
  bool CopyEntriesRecursively(
      const catalog::Catalog *source_catalog,
      catalog::WritableCatalog *dest_catalog,
      const std::string &current_path,
      bool is_upper_layer);

  /**
   * Get the parent directory path from a full path
   */
  std::string GetParentPath(const std::string &path);

 private:
  static const std::string kWhiteoutPrefix;
  static const std::string kOpaqueDirectoryMarker;
};

// Template implementation
template<class ObjectFetcherT>
catalog::WritableCatalog* CommandOverlay::OverlayCatalogs(
    const std::string &lower_catalog_path,
    const std::string &upper_catalog_path,
    const std::string &output_path,
    ObjectFetcherT *object_fetcher) {

  LogCvmfs(kLogCatalog, kLogStdout, "Loading lower catalog: %s",
           lower_catalog_path.c_str());

  // For simplicity, we'll load catalogs directly using AttachFreely
  // This assumes the catalog paths are local file paths
  catalog::Catalog *lower_catalog = catalog::Catalog::AttachFreely(
      "", lower_catalog_path, shash::Any());

  if (lower_catalog == NULL) {
    LogCvmfs(kLogCatalog, kLogStderr,
             "Failed to load lower catalog: %s", lower_catalog_path.c_str());
    return NULL;
  }

  LogCvmfs(kLogCatalog, kLogStdout, "Loading upper catalog: %s",
           upper_catalog_path.c_str());

  // Load the upper (overlay) catalog
  catalog::Catalog *upper_catalog = catalog::Catalog::AttachFreely(
      "", upper_catalog_path, shash::Any());

  if (upper_catalog == NULL) {
    LogCvmfs(kLogCatalog, kLogStderr,
             "Failed to load upper catalog: %s", upper_catalog_path.c_str());
    delete lower_catalog;
    return NULL;
  }

  // Create a temporary file for the output catalog
  const std::string temp_catalog_path = CreateTempPath("/tmp/overlay_catalog", 0600);
  if (temp_catalog_path.empty()) {
    LogCvmfs(kLogCatalog, kLogStderr, "Failed to create temporary catalog file");
    delete lower_catalog;
    delete upper_catalog;
    return NULL;
  }

  // Create the catalog database
  catalog::CatalogDatabase *catalog_db = catalog::CatalogDatabase::Create(temp_catalog_path);
  if (!catalog_db) {
    LogCvmfs(kLogCatalog, kLogStderr, "Failed to create catalog database");
    delete lower_catalog;
    delete upper_catalog;
    unlink(temp_catalog_path.c_str());
    return NULL;
  }

  // Initialize the catalog with a root entry
  const bool volatile_content = false;
  const std::string voms_authz = "";
  catalog::DirectoryEntry root_entry;  // Default root directory entry
  if (!catalog_db->InsertInitialValues("", volatile_content, voms_authz, root_entry)) {
    LogCvmfs(kLogCatalog, kLogStderr, "Failed to initialize catalog database");
    delete catalog_db;
    delete lower_catalog;
    delete upper_catalog;
    unlink(temp_catalog_path.c_str());
    return NULL;
  }
  delete catalog_db;

  // Create the output catalog
  catalog::WritableCatalog *output_catalog =
      catalog::WritableCatalog::AttachFreely("", temp_catalog_path, shash::Any());
  if (!output_catalog) {
    LogCvmfs(kLogCatalog, kLogStderr, "Failed to create output catalog");
    delete lower_catalog;
    delete upper_catalog;
    unlink(temp_catalog_path.c_str());
    return NULL;
  }

  // Copy entries from lower catalog first (base layer)
  LogCvmfs(kLogCatalog, kLogStdout, "Copying entries from lower catalog...");
  if (!CopyEntriesRecursively(lower_catalog, output_catalog, "", false)) {
    LogCvmfs(kLogCatalog, kLogStderr, "Failed to copy entries from lower catalog");
    delete output_catalog;
    delete lower_catalog;
    delete upper_catalog;
    unlink(temp_catalog_path.c_str());
    return NULL;
  }

  // Copy entries from upper catalog (overlay layer)
  LogCvmfs(kLogCatalog, kLogStdout, "Copying entries from upper catalog...");
  if (!CopyEntriesRecursively(upper_catalog, output_catalog, "", true)) {
    LogCvmfs(kLogCatalog, kLogStderr, "Failed to copy entries from upper catalog");
    delete output_catalog;
    delete lower_catalog;
    delete upper_catalog;
    unlink(temp_catalog_path.c_str());
    return NULL;
  }

  // Clean up source catalogs
  delete lower_catalog;
  delete upper_catalog;

  // Move the temporary catalog to the final output path
  if (rename(temp_catalog_path.c_str(), output_path.c_str()) != 0) {
    LogCvmfs(kLogCatalog, kLogStderr, "Failed to move catalog to output path: %s",
             output_path.c_str());
    delete output_catalog;
    unlink(temp_catalog_path.c_str());
    return NULL;
  }

  return output_catalog;
}

}  // namespace swissknife

#endif  // CVMFS_SWISSKNIFE_OVERLAY_H_
