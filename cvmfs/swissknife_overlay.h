/**
 * This file is part of the CernVM File System.
 */

#ifndef CVMFS_SWISSKNIFE_OVERLAY_H_
#define CVMFS_SWISSKNIFE_OVERLAY_H_

#include <string>
#include <vector>
#include <map>

#include "catalog_traversal.h"
#include "crypto/hash.h"
#include "directory_entry.h"
#include "object_fetcher.h"
#include "swissknife.h"
#include "util/string.h"

namespace catalog {
class Catalog;
}

namespace swissknife {

/**
 * This command overlays multiple CVMFS catalogs to create a new catalog.
 * It takes N paths corresponding to subdirectories in a repository that are
 * nested catalogs and creates a new catalog/directory location in the repository
 * that is the overlay of the previous catalogs.
 * 
 * If there are conflicting entries, the one from the last input catalog gets
 * preference. If the catalog contains whiteout markers and other special files
 * used in docker tarballs, they are respected and no entry is added to the
 * final catalog.
 * 
 * Docker whiteout files:
 * - .wh.<filename> - indicates that <filename> should be deleted/hidden
 * - .wh..wh..opq - indicates that the directory should be opaque (all previous
 *   content hidden)
 */
class CommandOverlay : public Command {
 public:
  CommandOverlay();
  ~CommandOverlay() { }
  
  virtual std::string GetName() const { return "overlay"; }
  virtual std::string GetDescription() const {
    return "Overlay multiple CVMFS catalogs to create a new catalog\n"
           "This command takes N paths corresponding to subdirectories in a "
           "repository that are nested catalogs and creates a new catalog at "
           "the specified output location that is the overlay of the input "
           "catalogs. If there are conflicting entries, the one from the last "
           "input catalog gets preference. Docker whiteout files (.wh. prefix) "
           "are respected and cause entries to be hidden/deleted.";
  }
  
  virtual ParameterList GetParams() const;
  virtual int Main(const ArgumentList &args);

 protected:
  /**
   * Structure to hold information about a catalog entry during overlay processing
   */
  struct OverlayEntry {
    catalog::DirectoryEntry dirent;
    std::string full_path;
    std::string parent_path;
    int source_catalog_index;  // Which input catalog this entry came from
    bool is_whiteout;          // True if this is a whiteout marker
    bool is_opaque_dir;        // True if this is an opaque directory marker
    std::string whiteout_target; // For whiteout files, the target being whited out
    
    OverlayEntry() : source_catalog_index(-1), is_whiteout(false), 
                     is_opaque_dir(false) {}
  };

  /**
   * Map from path to overlay entry. Used to track all entries and resolve conflicts.
   */
  typedef std::map<std::string, OverlayEntry> OverlayMap;

  template<class ObjectFetcherT>
  bool Run(const std::vector<std::string> &input_paths,
           const std::string &output_path,
           const std::string &repo_url,
           ObjectFetcherT *object_fetcher);

  /**
   * Process a single catalog and add its entries to the overlay map
   */
  template<class ObjectFetcherT>
  bool ProcessCatalog(const std::string &catalog_path,
                      int catalog_index,
                      ObjectFetcherT *object_fetcher,
                      OverlayMap *overlay_map);

  /**
   * Recursively process all entries in a catalog directory
   */
  bool ProcessCatalogRecursively(const catalog::Catalog *catalog,
                                const std::string &catalog_root,
                                const std::string &current_path,
                                int catalog_index,
                                OverlayMap *overlay_map);

  /**
   * Check if a filename is a Docker whiteout marker
   */
  bool IsWhiteoutFile(const std::string &filename, std::string *target = nullptr);
  
  /**
   * Check if a filename is an opaque directory marker
   */
  bool IsOpaqueDirectoryMarker(const std::string &filename);
  
  /**
   * Apply whiteout rules to the overlay map
   */
  void ApplyWhiteouts(OverlayMap *overlay_map);
  
  /**
   * Create the output catalog with the final overlay entries
   */
  bool CreateOutputCatalog(const std::string &output_path,
                          const OverlayMap &overlay_map);

 private:
  static const std::string kWhiteoutPrefix;
  static const std::string kOpaqueDirectoryMarker;
};

}  // namespace swissknife

#endif  // CVMFS_SWISSKNIFE_OVERLAY_H_
