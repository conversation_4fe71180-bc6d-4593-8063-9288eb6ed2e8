/**
 * This file is part of the CernVM File System.
 */

#include "swissknife_overlay.h"

#include <string>
#include <vector>
#include <map>
#include <algorithm>

#include "catalog.h"
#include "catalog_rw.h"
#include "catalog_mgr_rw.h"
#include "directory_entry.h"
#include "util/logging.h"
#include "util/posix.h"
#include "util/string.h"
#include "util/platform.h"

namespace swissknife {

const std::string CommandOverlay::kWhiteoutPrefix = ".wh.";
const std::string CommandOverlay::kOpaqueDirectoryMarker = ".wh..wh..opq";

CommandOverlay::CommandOverlay() {
}

ParameterList CommandOverlay::GetParams() const {
  ParameterList r;
  r.push_back(Parameter::Mandatory(
      'r', "repository URL (absolute local path or remote URL)"));
  r.push_back(Parameter::Mandatory(
      'i', "comma-separated list of input catalog paths"));
  r.push_back(Parameter::Mandatory(
      'o', "output catalog path"));
  r.push_back(Parameter::Optional('n', "fully qualified repository name"));
  r.push_back(Parameter::Optional('k', "repository master key(s) / dir"));
  r.push_back(Parameter::Optional('l', "temporary directory"));
  r.push_back(Parameter::Optional('@', "proxy url"));
  r.push_back(Parameter::Switch('v', "verbose output"));
  return r;
}

int CommandOverlay::Main(const ArgumentList &args) {
  const std::string &repo_url = *args.find('r')->second;
  const std::string &input_paths_str = *args.find('i')->second;
  const std::string &output_path = *args.find('o')->second;
  const std::string &repo_name = (args.count('n') > 0) ? *args.find('n')->second
                                                       : "";
  std::string repo_keys = (args.count('k') > 0) ? *args.find('k')->second : "";
  if (DirectoryExists(repo_keys))
    repo_keys = JoinStrings(FindFilesBySuffix(repo_keys, ".pub"), ":");
  const std::string &tmp_dir = (args.count('l') > 0) ? *args.find('l')->second
                                                     : "/tmp";
  const bool verbose = (args.count('v') > 0);

  // Parse input paths
  std::vector<std::string> input_paths = SplitString(input_paths_str, ',');
  if (input_paths.empty()) {
    LogCvmfs(kLogCatalog, kLogStderr, "No input catalog paths provided");
    return 1;
  }

  if (verbose) {
    LogCvmfs(kLogCatalog, kLogStdout, "Overlaying %lu catalogs:", input_paths.size());
    for (size_t i = 0; i < input_paths.size(); ++i) {
      LogCvmfs(kLogCatalog, kLogStdout, "  [%lu] %s", i, input_paths[i].c_str());
    }
    LogCvmfs(kLogCatalog, kLogStdout, "Output: %s", output_path.c_str());
  }

  bool success = false;
  if (IsHttpUrl(repo_url)) {
    const bool follow_redirects = false;
    const std::string proxy = ((args.count('@') > 0) ? *args.find('@')->second
                                                     : "");
    if (!this->InitDownloadManager(follow_redirects, proxy)
        || !this->InitSignatureManager(repo_keys)) {
      LogCvmfs(kLogCatalog, kLogStderr, "Failed to init remote connection");
      return 1;
    }

    HttpObjectFetcher<catalog::Catalog, history::SqliteHistory> fetcher(
        repo_name, repo_url, tmp_dir, download_manager(), signature_manager());
    success = Run(input_paths, output_path, repo_url, &fetcher);
  } else {
    LocalObjectFetcher<> fetcher(repo_url, tmp_dir);
    success = Run(input_paths, output_path, repo_url, &fetcher);
  }

  return success ? 0 : 1;
}

template<class ObjectFetcherT>
bool CommandOverlay::Run(const std::vector<std::string> &input_paths,
                         const std::string &output_path,
                         const std::string &repo_url,
                         ObjectFetcherT *object_fetcher) {
  OverlayMap overlay_map;

  // Process each input catalog in order
  for (size_t i = 0; i < input_paths.size(); ++i) {
    LogCvmfs(kLogCatalog, kLogStdout, "Processing catalog %lu: %s", 
             i, input_paths[i].c_str());
    
    if (!ProcessCatalog(input_paths[i], static_cast<int>(i), 
                       object_fetcher, &overlay_map)) {
      LogCvmfs(kLogCatalog, kLogStderr, "Failed to process catalog: %s", 
               input_paths[i].c_str());
      return false;
    }
  }

  // Apply whiteout rules
  LogCvmfs(kLogCatalog, kLogStdout, "Applying whiteout rules...");
  ApplyWhiteouts(&overlay_map);

  // Create output catalog
  LogCvmfs(kLogCatalog, kLogStdout, "Creating output catalog: %s", 
           output_path.c_str());
  if (!CreateOutputCatalog(output_path, overlay_map)) {
    LogCvmfs(kLogCatalog, kLogStderr, "Failed to create output catalog");
    return false;
  }

  LogCvmfs(kLogCatalog, kLogStdout, "Overlay completed successfully");
  return true;
}

bool CommandOverlay::IsWhiteoutFile(const std::string &filename, 
                                   std::string *target) {
  if (filename.length() <= kWhiteoutPrefix.length()) {
    return false;
  }
  
  if (filename.substr(0, kWhiteoutPrefix.length()) == kWhiteoutPrefix) {
    if (target) {
      *target = filename.substr(kWhiteoutPrefix.length());
    }
    return true;
  }
  
  return false;
}

bool CommandOverlay::IsOpaqueDirectoryMarker(const std::string &filename) {
  return filename == kOpaqueDirectoryMarker;
}

template<class ObjectFetcherT>
bool CommandOverlay::ProcessCatalog(const std::string &catalog_path,
                                    int catalog_index,
                                    ObjectFetcherT *object_fetcher,
                                    OverlayMap *overlay_map) {
  LogCvmfs(kLogCatalog, kLogStdout, "Processing catalog at path: %s",
           catalog_path.c_str());

  // Load the catalog using the object fetcher
  typename ObjectFetcherT::CatalogTN *catalog = NULL;
  const LoadReturn load_result = object_fetcher->LoadCatalog(
      PathString(catalog_path.data(), catalog_path.length()),
      shash::Any(),  // We'll need to resolve this properly
      std::string(""), // Description
      &catalog);

  if (load_result != kLoadNew || catalog == NULL) {
    LogCvmfs(kLogCatalog, kLogStderr,
             "Failed to load catalog at path: %s", catalog_path.c_str());
    return false;
  }

  // Recursively process all entries in the catalog
  bool success = ProcessCatalogRecursively(catalog, catalog_path, "",
                                          catalog_index, overlay_map);

  // Clean up
  object_fetcher->UnloadCatalog(catalog);

  return success;
}

bool CommandOverlay::ProcessCatalogRecursively(
    const catalog::Catalog *catalog,
    const std::string &catalog_root,
    const std::string &current_path,
    int catalog_index,
    CommandOverlay::OverlayMap *overlay_map) {

  catalog::DirectoryEntryList entries;
  PathString path_string(current_path.data(), current_path.length());

  if (!catalog->ListingPath(path_string, &entries)) {
    LogCvmfs(kLogCatalog, kLogStderr,
             "Failed to list directory: %s", current_path.c_str());
    return false;
  }

  // Process each entry in the current directory
  for (catalog::DirectoryEntryList::const_iterator it = entries.begin();
       it != entries.end(); ++it) {
    const catalog::DirectoryEntry &entry = *it;

    // Construct full path for this entry
    std::string entry_path = current_path;
    if (!entry_path.empty() && entry_path != "/") {
      entry_path += "/";
    }
    entry_path += entry.name().ToString();

    // Create overlay entry
    CommandOverlay::OverlayEntry overlay_entry;
    overlay_entry.dirent = entry;
    overlay_entry.full_path = entry_path;
    overlay_entry.parent_path = current_path;
    overlay_entry.source_catalog_index = catalog_index;

    // Check if this is a whiteout file
    std::string filename = entry.name().ToString();
    std::string whiteout_target;
    if (IsWhiteoutFile(filename, &whiteout_target)) {
      overlay_entry.is_whiteout = true;
      overlay_entry.whiteout_target = whiteout_target;
    } else if (IsOpaqueDirectoryMarker(filename)) {
      overlay_entry.is_opaque_dir = true;
    }

    // Add to overlay map (this will overwrite entries from earlier catalogs)
    (*overlay_map)[entry_path] = overlay_entry;

    // If this is a directory (and not a whiteout), recurse into it
    if (entry.IsDirectory() && !overlay_entry.is_whiteout &&
        !overlay_entry.is_opaque_dir) {
      if (!ProcessCatalogRecursively(catalog, catalog_root, entry_path,
                                    catalog_index, overlay_map)) {
        return false;
      }
    }
  }

  return true;
}

void CommandOverlay::ApplyWhiteouts(CommandOverlay::OverlayMap *overlay_map) {
  std::vector<std::string> paths_to_remove;
  std::vector<std::string> opaque_dirs;

  // First pass: identify whiteout files and opaque directories
  for (CommandOverlay::OverlayMap::iterator it = overlay_map->begin();
       it != overlay_map->end(); ++it) {
    const std::string &path = it->first;
    const CommandOverlay::OverlayEntry &entry = it->second;

    if (entry.is_whiteout) {
      // This is a whiteout file - mark the target for removal
      std::string parent_dir = GetParentPath(path);
      std::string target_path = parent_dir + "/" + entry.whiteout_target;
      paths_to_remove.push_back(target_path);
      // Also remove the whiteout file itself
      paths_to_remove.push_back(path);
    } else if (entry.is_opaque_dir) {
      // This is an opaque directory marker
      std::string parent_dir = GetParentPath(path);
      opaque_dirs.push_back(parent_dir);
      // Remove the opaque marker itself
      paths_to_remove.push_back(path);
    }
  }

  // Second pass: remove whited-out entries
  for (const std::string &path : paths_to_remove) {
    overlay_map->erase(path);
  }

  // Third pass: handle opaque directories
  for (const std::string &opaque_dir : opaque_dirs) {
    // Remove all entries from lower layers in this directory
    std::vector<std::string> to_remove;
    for (CommandOverlay::OverlayMap::iterator it = overlay_map->begin();
         it != overlay_map->end(); ++it) {
      const std::string &entry_path = it->first;
      const CommandOverlay::OverlayEntry &entry = it->second;

      // Check if this entry is in the opaque directory and from a lower layer
      if (HasPrefix(entry_path, opaque_dir + "/", false)) {
        // Find the highest layer index for this directory
        int highest_layer = -1;
        for (CommandOverlay::OverlayMap::iterator it2 = overlay_map->begin();
             it2 != overlay_map->end(); ++it2) {
          if (HasPrefix(it2->first, opaque_dir + "/", false)) {
            highest_layer = std::max(highest_layer,
                                   it2->second.source_catalog_index);
          }
        }

        // Remove entries from lower layers
        if (entry.source_catalog_index < highest_layer) {
          to_remove.push_back(entry_path);
        }
      }
    }

    for (const std::string &path : to_remove) {
      overlay_map->erase(path);
    }
  }
}

bool CommandOverlay::CreateOutputCatalog(const std::string &output_path,
                                         const CommandOverlay::OverlayMap &overlay_map) {
  LogCvmfs(kLogCatalog, kLogStdout,
           "Creating output catalog with %lu entries",
           overlay_map.size());

  // Create a temporary file for the catalog database
  const std::string temp_catalog_path = CreateTempPath("/tmp/overlay_catalog", 0600);
  if (temp_catalog_path.empty()) {
    LogCvmfs(kLogCatalog, kLogStderr, "Failed to create temporary catalog file");
    return false;
  }

  // Create the catalog database
  catalog::CatalogDatabase *catalog_db = catalog::CatalogDatabase::Create(temp_catalog_path);
  if (!catalog_db) {
    LogCvmfs(kLogCatalog, kLogStderr, "Failed to create catalog database");
    unlink(temp_catalog_path.c_str());
    return false;
  }

  // Create a simple root directory entry
  // We'll use a default directory entry and let the catalog system handle the details
  catalog::DirectoryEntry root_entry;

  // Initialize the catalog with root entry
  const bool volatile_content = false;
  const std::string voms_authz = "";
  if (!catalog_db->InsertInitialValues(output_path, volatile_content, voms_authz, root_entry)) {
    LogCvmfs(kLogCatalog, kLogStderr, "Failed to initialize catalog database");
    delete catalog_db;
    unlink(temp_catalog_path.c_str());
    return false;
  }
  delete catalog_db;

  // Create a WritableCatalog to add entries
  catalog::WritableCatalog *writable_catalog =
      catalog::WritableCatalog::AttachFreely(output_path, temp_catalog_path, shash::Any());
  if (!writable_catalog) {
    LogCvmfs(kLogCatalog, kLogStderr, "Failed to attach writable catalog");
    unlink(temp_catalog_path.c_str());
    return false;
  }

  // Start a transaction for better performance
  writable_catalog->Transaction();

  // Add all entries from the overlay map
  XattrList empty_xattrs;
  for (CommandOverlay::OverlayMap::const_iterator it = overlay_map.begin();
       it != overlay_map.end(); ++it) {
    const std::string &entry_path = it->first;
    const CommandOverlay::OverlayEntry &overlay_entry = it->second;

    // Skip root directory (already added)
    if (entry_path.empty() || entry_path == "/") {
      continue;
    }

    // Add the entry to the catalog
    writable_catalog->AddEntry(overlay_entry.dirent, empty_xattrs,
                              entry_path, overlay_entry.parent_path);
  }

  // Commit the transaction
  writable_catalog->Commit();

  // Clean up
  delete writable_catalog;

  // Move the temporary catalog to the final output path
  if (rename(temp_catalog_path.c_str(), output_path.c_str()) != 0) {
    LogCvmfs(kLogCatalog, kLogStderr, "Failed to move catalog to output path: %s",
             output_path.c_str());
    unlink(temp_catalog_path.c_str());
    return false;
  }

  LogCvmfs(kLogCatalog, kLogStdout, "Successfully created output catalog: %s",
           output_path.c_str());
  return true;
}

}  // namespace swissknife
