/**
 * This file is part of the CernVM File System.
 */

#include "swissknife_overlay.h"

#include <string>
#include <vector>
#include <map>
#include <algorithm>

#include "catalog.h"
#include "catalog_rw.h"
#include "catalog_mgr_rw.h"
#include "catalog_sql.h"
#include "directory_entry.h"
#include "xattr.h"
#include "util/logging.h"
#include "util/posix.h"
#include "util/string.h"
#include "util/platform.h"



namespace swissknife {

const std::string CommandOverlay::kWhiteoutPrefix = ".wh.";
const std::string CommandOverlay::kOpaqueDirectoryMarker = ".wh..wh..opq";

CommandOverlay::CommandOverlay() {
}

ParameterList CommandOverlay::GetParams() const {
  ParameterList r;
  r.push_back(Parameter::Mandatory(
      'l', "lower catalog path (base layer)"));
  r.push_back(Parameter::Mandatory(
      'u', "upper catalog path (overlay layer)"));
  r.push_back(Parameter::Mandatory(
      'o', "output catalog path"));
  r.push_back(Parameter::Optional('r', "repository URL (absolute local path or remote URL)"));
  r.push_back(Parameter::Optional('n', "fully qualified repository name"));
  r.push_back(Parameter::Optional('k', "repository master key(s) / dir"));
  r.push_back(Parameter::Optional('t', "temporary directory"));
  r.push_back(Parameter::Optional('@', "proxy url"));
  r.push_back(Parameter::Switch('v', "verbose output"));
  return r;
}

int CommandOverlay::Main(const ArgumentList &args) {
  const std::string &lower_catalog_path = *args.find('l')->second;
  const std::string &upper_catalog_path = *args.find('u')->second;
  const std::string &output_path = *args.find('o')->second;
  const std::string repo_url = (args.count('r') > 0) ? *args.find('r')->second : "";
  const std::string &repo_name = (args.count('n') > 0) ? *args.find('n')->second : "";
  std::string repo_keys = (args.count('k') > 0) ? *args.find('k')->second : "";
  if (DirectoryExists(repo_keys))
    repo_keys = JoinStrings(FindFilesBySuffix(repo_keys, ".pub"), ":");
  const std::string &tmp_dir = (args.count('t') > 0) ? *args.find('t')->second : "/tmp";
  const bool verbose = (args.count('v') > 0);

  if (verbose) {
    LogCvmfs(kLogCatalog, kLogStdout, "Overlaying catalogs:");
    LogCvmfs(kLogCatalog, kLogStdout, "  Lower: %s", lower_catalog_path.c_str());
    LogCvmfs(kLogCatalog, kLogStdout, "  Upper: %s", upper_catalog_path.c_str());
    LogCvmfs(kLogCatalog, kLogStdout, "  Output: %s", output_path.c_str());
  }

  catalog::WritableCatalog *result_catalog = NULL;
  bool success = false;

  if (!repo_url.empty() && IsHttpUrl(repo_url)) {
    const bool follow_redirects = false;
    const std::string proxy = ((args.count('@') > 0) ? *args.find('@')->second : "");
    if (!this->InitDownloadManager(follow_redirects, proxy)
        || !this->InitSignatureManager(repo_keys)) {
      LogCvmfs(kLogCatalog, kLogStderr, "Failed to init remote connection");
      return 1;
    }

    HttpObjectFetcher<catalog::Catalog, history::SqliteHistory> fetcher(
        repo_name, repo_url, tmp_dir, download_manager(), signature_manager());
    result_catalog = OverlayCatalogs(lower_catalog_path, upper_catalog_path,
                                    output_path, &fetcher);
  } else {
    // Use local object fetcher for local catalogs
    std::string local_repo_url = repo_url.empty() ? "/" : repo_url;
    LocalObjectFetcher<> fetcher(local_repo_url, tmp_dir);
    result_catalog = OverlayCatalogs(lower_catalog_path, upper_catalog_path,
                                    output_path, &fetcher);
  }

  if (result_catalog) {
    success = true;
    delete result_catalog;
    if (verbose) {
      LogCvmfs(kLogCatalog, kLogStdout, "Successfully created overlay catalog: %s",
               output_path.c_str());
    }
  } else {
    LogCvmfs(kLogCatalog, kLogStderr, "Failed to create overlay catalog");
  }

  return success ? 0 : 1;
}



bool CommandOverlay::IsWhiteoutFile(const std::string &filename, 
                                   std::string *target) {
  if (filename.length() <= kWhiteoutPrefix.length()) {
    return false;
  }
  
  if (filename.substr(0, kWhiteoutPrefix.length()) == kWhiteoutPrefix) {
    if (target) {
      *target = filename.substr(kWhiteoutPrefix.length());
    }
    return true;
  }
  
  return false;
}

bool CommandOverlay::IsOpaqueDirectoryMarker(const std::string &filename) {
  return filename == kOpaqueDirectoryMarker;
}

std::string CommandOverlay::GetParentPath(const std::string &path) {
  size_t last_slash = path.find_last_of('/');
  if (last_slash == std::string::npos) {
    return "";
  }
  return path.substr(0, last_slash);
}

bool CommandOverlay::CopyEntriesRecursively(
    const catalog::Catalog *source_catalog,
    catalog::WritableCatalog *dest_catalog,
    const std::string &current_path,
    bool is_upper_layer) {
  catalog::DirectoryEntryList entries;
  PathString path_string(current_path.data(), current_path.length());

  if (!source_catalog->ListingPath(path_string, &entries)) {
    LogCvmfs(kLogCatalog, kLogStderr,
             "Failed to list directory: %s", current_path.c_str());
    return false;
  }

  // Process each entry in the current directory
  for (catalog::DirectoryEntryList::const_iterator it = entries.begin();
       it != entries.end(); ++it) {
    const catalog::DirectoryEntry &entry = *it;

    // Construct full path for this entry
    std::string entry_path = current_path;
    if (!entry_path.empty() && entry_path != "/") {
      entry_path += "/";
    }
    entry_path += entry.name().ToString();

    std::string filename = entry.name().ToString();

    // Handle whiteout files in upper layer
    if (is_upper_layer) {
      std::string whiteout_target;
      if (IsWhiteoutFile(filename, &whiteout_target)) {
        // This is a whiteout file - remove the target from destination
        std::string target_path = current_path;
        if (!target_path.empty() && target_path != "/") {
          target_path += "/";
        }
        target_path += whiteout_target;

        // Check if the target exists before trying to remove it
        catalog::DirectoryEntry existing_entry;
        PathString target_path_string(target_path.data(), target_path.length());
        if (dest_catalog->LookupPath(target_path_string, &existing_entry)) {
          dest_catalog->RemoveEntry(target_path);
        }
        continue; // Don't add the whiteout file itself
      }

      if (IsOpaqueDirectoryMarker(filename)) {
        // This directory should be opaque - remove all lower layer content
        // For now, we'll just skip adding the marker file
        continue;
      }
    }

    // If this is the upper layer and entry already exists, remove it first
    if (is_upper_layer) {
      catalog::DirectoryEntry existing_entry;
      PathString entry_path_string(entry_path.data(), entry_path.length());
      if (dest_catalog->LookupPath(entry_path_string, &existing_entry)) {
        dest_catalog->RemoveEntry(entry_path);
      }
    }

    // Add the entry with empty xattrs
    XattrList empty_xattrs;
    std::string parent_path = GetParentPath(entry_path);
    dest_catalog->AddEntry(entry, empty_xattrs, entry_path, parent_path);

    // If this is a directory, recursively process its contents
    if (entry.IsDirectory()) {
      if (!CopyEntriesRecursively(source_catalog, dest_catalog, entry_path, is_upper_layer)) {
        return false;
      }
    }
  }

  return true;
}

}  // namespace swissknife
